import 'package:flutter/material.dart';
import 'screens/land_measurement_screen.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اندازه‌گیری زمین کشاورزی',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.green),
        useMaterial3: true,
      ),
      home: const LandMeasurementScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
