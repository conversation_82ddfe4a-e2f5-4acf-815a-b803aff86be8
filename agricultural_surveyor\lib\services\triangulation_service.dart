import 'dart:math';
import '../models/point.dart';
import '../models/triangle.dart';

/// سرویس تقسیم چندضلعی به مثلث‌ها
class TriangulationService {
  
  /// تقسیم چندضلعی به مثلث‌ها با استفاده از الگوریتم Ear Clipping
  static List<Triangle> earClippingTriangulation(List<Point> polygon) {
    if (polygon.length < 3) return [];
    
    if (polygon.length == 3) {
      return [Triangle(a: polygon[0], b: polygon[1], c: polygon[2])];
    }

    final triangles = <Triangle>[];
    final vertices = List<Point>.from(polygon);

    // ادامه تا زمانی که فقط سه نقطه باقی بماند
    while (vertices.length > 3) {
      bool earFound = false;

      for (int i = 0; i < vertices.length; i++) {
        final prev = vertices[(i - 1 + vertices.length) % vertices.length];
        final curr = vertices[i];
        final next = vertices[(i + 1) % vertices.length];

        // بررسی اینکه آیا این گوش (ear) است یا نه
        if (_isEar(prev, curr, next, vertices)) {
          // اضافه کردن مثلث به لیست
          triangles.add(Triangle(a: prev, b: curr, c: next));
          
          // حذف نقطه فعلی از لیست
          vertices.removeAt(i);
          earFound = true;
          break;
        }
      }

      // اگر هیچ گوشی پیدا نشد، از الگوریتم Fan استفاده کن
      if (!earFound) {
        triangles.addAll(_fanTriangulation(vertices));
        break;
      }
    }

    // اضافه کردن آخرین مثلث
    if (vertices.length == 3) {
      triangles.add(Triangle(a: vertices[0], b: vertices[1], c: vertices[2]));
    }

    return triangles;
  }

  /// بررسی اینکه آیا سه نقطه یک گوش تشکیل می‌دهند یا نه
  static bool _isEar(Point prev, Point curr, Point next, List<Point> vertices) {
    // بررسی اینکه آیا مثلث محدب است یا نه
    if (!_isConvex(prev, curr, next)) {
      return false;
    }

    // بررسی اینکه آیا نقطه‌ای داخل مثلث قرار دارد یا نه
    for (final vertex in vertices) {
      if (vertex == prev || vertex == curr || vertex == next) {
        continue;
      }

      if (_isPointInTriangle(vertex, prev, curr, next)) {
        return false;
      }
    }

    return true;
  }

  /// بررسی محدب بودن سه نقطه
  static bool _isConvex(Point a, Point b, Point c) {
    final crossProduct = (b.x - a.x) * (c.y - a.y) - (b.y - a.y) * (c.x - a.x);
    return crossProduct > 0; // برای چندضلعی‌های در جهت عقربه‌های ساعت
  }

  /// بررسی اینکه آیا نقطه داخل مثلث قرار دارد یا نه
  static bool _isPointInTriangle(Point p, Point a, Point b, Point c) {
    final denom = (b.y - c.y) * (a.x - c.x) + (c.x - b.x) * (a.y - c.y);
    
    if (denom == 0) return false; // مثلث تبدیل به خط شده

    final alpha = ((b.y - c.y) * (p.x - c.x) + (c.x - b.x) * (p.y - c.y)) / denom;
    final beta = ((c.y - a.y) * (p.x - c.x) + (a.x - c.x) * (p.y - c.y)) / denom;
    final gamma = 1 - alpha - beta;

    return alpha >= 0 && beta >= 0 && gamma >= 0;
  }

  /// الگوریتم Fan Triangulation (ساده‌تر اما کمتر بهینه)
  static List<Triangle> _fanTriangulation(List<Point> vertices) {
    final triangles = <Triangle>[];
    
    if (vertices.length < 3) return triangles;

    final pivot = vertices[0];
    
    for (int i = 1; i < vertices.length - 1; i++) {
      triangles.add(Triangle(
        a: pivot,
        b: vertices[i],
        c: vertices[i + 1],
      ));
    }

    return triangles;
  }

  /// تقسیم ساده با استفاده از Fan Triangulation
  static List<Triangle> simpleTriangulation(List<Point> polygon) {
    return _fanTriangulation(polygon);
  }

  /// محاسبه مرکز چندضلعی
  static Point calculateCentroid(List<Point> points) {
    if (points.isEmpty) return const Point(x: 0, y: 0);

    double sumX = 0;
    double sumY = 0;

    for (final point in points) {
      sumX += point.x;
      sumY += point.y;
    }

    return Point(
      x: sumX / points.length,
      y: sumY / points.length,
    );
  }

  /// بررسی اینکه آیا چندضلعی در جهت عقربه‌های ساعت است یا نه
  static bool isClockwise(List<Point> points) {
    if (points.length < 3) return true;

    double sum = 0;
    for (int i = 0; i < points.length; i++) {
      final current = points[i];
      final next = points[(i + 1) % points.length];
      sum += (next.x - current.x) * (next.y + current.y);
    }

    return sum > 0;
  }

  /// تبدیل چندضلعی به جهت عقربه‌های ساعت
  static List<Point> ensureClockwise(List<Point> points) {
    if (isClockwise(points)) {
      return points;
    } else {
      return points.reversed.toList();
    }
  }
}
