import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/point.dart';
import '../services/area_calculator.dart';
import '../services/triangulation_service.dart';

class LandMeasurementScreen extends StatefulWidget {
  const LandMeasurementScreen({super.key});

  @override
  State<LandMeasurementScreen> createState() => _LandMeasurementScreenState();
}

class _LandMeasurementScreenState extends State<LandMeasurementScreen> {
  final List<Point> _points = [];
  TriangulationResult? _result;
  bool _showTriangles = false;

  void _addPoint(Offset offset) {
    setState(() {
      _points.add(Point(x: offset.dx, y: offset.dy));
      _calculateArea();
    });
  }

  void _calculateArea() {
    if (_points.length >= 3) {
      _result = AreaCalculator.triangulateAndCalculate(_points);
    } else {
      _result = null;
    }
  }

  void _clearPoints() {
    setState(() {
      _points.clear();
      _result = null;
    });
  }

  void _removeLastPoint() {
    if (_points.isNotEmpty) {
      setState(() {
        _points.removeLast();
        _calculateArea();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اندازه‌گیری زمین کشاورزی'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: Icon(_showTriangles ? Icons.visibility_off : Icons.visibility),
            onPressed: () {
              setState(() {
                _showTriangles = !_showTriangles;
              });
            },
            tooltip: _showTriangles ? 'مخفی کردن مثلث‌ها' : 'نمایش مثلث‌ها',
          ),
        ],
      ),
      body: Column(
        children: [
          // منطقه رسم
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: GestureDetector(
                onTapDown: (details) {
                  final RenderBox renderBox = context.findRenderObject() as RenderBox;
                  final localPosition = renderBox.globalToLocal(details.globalPosition);
                  
                  // تنظیم موقعیت نسبت به Container
                  final adjustedPosition = Offset(
                    localPosition.dx - 8.0,
                    localPosition.dy - 8.0 - kToolbarHeight - MediaQuery.of(context).padding.top,
                  );
                  
                  if (adjustedPosition.dx >= 0 && adjustedPosition.dy >= 0) {
                    _addPoint(adjustedPosition);
                  }
                },
                child: CustomPaint(
                  painter: LandPlotPainter(
                    points: _points,
                    triangles: _showTriangles ? _result?.triangles ?? [] : [],
                  ),
                  size: Size.infinite,
                ),
              ),
            ),
          ),
          
          // اطلاعات محاسبات
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تعداد نقاط: ${_points.length}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  
                  if (_result != null) ...[
                    Text(
                      'تعداد مثلث‌ها: ${_result!.triangleCount}',
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                    const SizedBox(height: 8),
                    
                    Text(
                      'مساحت کل: ${_result!.totalArea.toStringAsFixed(2)} واحد مربع',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.green[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    if (_result!.triangles.isNotEmpty) ...[
                      const Text('مساحت هر مثلث:'),
                      const SizedBox(height: 4),
                      Expanded(
                        child: ListView.builder(
                          itemCount: _result!.triangles.length,
                          itemBuilder: (context, index) {
                            final triangle = _result!.triangles[index];
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2.0),
                              child: Text(
                                'مثلث ${index + 1}: ${triangle.area.toStringAsFixed(2)} واحد مربع',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ] else if (_points.length > 0) ...[
                    const Text(
                      'حداقل ۳ نقطه برای محاسبه مساحت لازم است',
                      style: TextStyle(color: Colors.orange),
                    ),
                  ] else ...[
                    const Text(
                      'روی صفحه ضربه بزنید تا نقاط زمین را مشخص کنید',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
      
      bottomNavigationBar: BottomAppBar(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ElevatedButton.icon(
              onPressed: _points.isNotEmpty ? _removeLastPoint : null,
              icon: const Icon(Icons.undo),
              label: const Text('حذف آخرین نقطه'),
            ),
            ElevatedButton.icon(
              onPressed: _points.isNotEmpty ? _clearPoints : null,
              icon: const Icon(Icons.clear),
              label: const Text('پاک کردن همه'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[100],
                foregroundColor: Colors.red[700],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// کلاس رسم‌کننده برای نمایش زمین و مثلث‌ها
class LandPlotPainter extends CustomPainter {
  final List<Point> points;
  final List<dynamic> triangles; // List<Triangle> but using dynamic to avoid import issues

  LandPlotPainter({
    required this.points,
    required this.triangles,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final pointPaint = Paint()
      ..color = Colors.red
      ..strokeWidth = 8.0
      ..style = PaintingStyle.fill;

    final linePaint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final trianglePaint = Paint()
      ..color = Colors.green.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    final triangleBorderPaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // رسم مثلث‌ها (اگر نمایش داده شوند)
    for (final triangle in triangles) {
      final path = Path();
      path.moveTo(triangle.a.x, triangle.a.y);
      path.lineTo(triangle.b.x, triangle.b.y);
      path.lineTo(triangle.c.x, triangle.c.y);
      path.close();

      canvas.drawPath(path, trianglePaint);
      canvas.drawPath(path, triangleBorderPaint);
    }

    // رسم خطوط بین نقاط
    if (points.length > 1) {
      final path = Path();
      path.moveTo(points[0].x, points[0].y);
      
      for (int i = 1; i < points.length; i++) {
        path.lineTo(points[i].x, points[i].y);
      }
      
      // اتصال آخرین نقطه به اولین نقطه (اگر بیش از ۲ نقطه داریم)
      if (points.length > 2) {
        path.lineTo(points[0].x, points[0].y);
      }
      
      canvas.drawPath(path, linePaint);
    }

    // رسم نقاط
    for (int i = 0; i < points.length; i++) {
      final point = points[i];
      canvas.drawCircle(Offset(point.x, point.y), 6.0, pointPaint);
      
      // نمایش شماره نقطه
      final textPainter = TextPainter(
        text: TextSpan(
          text: '${i + 1}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(point.x - textPainter.width / 2, point.y - textPainter.height / 2),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
