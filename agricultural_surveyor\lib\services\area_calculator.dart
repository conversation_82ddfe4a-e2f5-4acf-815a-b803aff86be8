import 'dart:math';
import '../models/point.dart';
import '../models/triangle.dart';

/// سرویس محاسبه مساحت زمین
class AreaCalculator {
  
  /// محاسبه مساحت یک مثلث با استفاده از فرمول هرون
  static double calculateTriangleArea(Point a, Point b, Point c) {
    final triangle = Triangle(a: a, b: b, c: c);
    return triangle.area;
  }

  /// محاسبه مساحت کل چندضلعی با تقسیم به مثلث‌ها
  static double calculatePolygonArea(List<Point> points) {
    if (points.length < 3) {
      return 0.0; // حداقل سه نقطه برای یک چندضلعی لازم است
    }

    // استفاده از الگوریتم Shoelace برای محاسبه مساحت چندضلعی
    return _calculateUsingShoelaceFormula(points);
  }

  /// محاسبه مساحت با استفاده از فرمول Shoelace
  static double _calculateUsingShoelaceFormula(List<Point> points) {
    if (points.length < 3) return 0.0;

    double area = 0.0;
    final n = points.length;

    for (int i = 0; i < n; i++) {
      final j = (i + 1) % n;
      area += points[i].x * points[j].y;
      area -= points[j].x * points[i].y;
    }

    return (area / 2).abs();
  }

  /// تقسیم چندضلعی به مثلث‌ها و محاسبه مساحت کل
  static TriangulationResult triangulateAndCalculate(List<Point> points) {
    if (points.length < 3) {
      return TriangulationResult(
        triangles: [],
        totalArea: 0.0,
        isValid: false,
      );
    }

    if (points.length == 3) {
      // اگر فقط سه نقطه داریم، یک مثلث است
      final triangle = Triangle(a: points[0], b: points[1], c: points[2]);
      return TriangulationResult(
        triangles: [triangle],
        totalArea: triangle.area,
        isValid: triangle.isValid,
      );
    }

    // برای چندضلعی‌های پیچیده‌تر، از الگوریتم Fan Triangulation استفاده می‌کنیم
    return _fanTriangulation(points);
  }

  /// الگوریتم Fan Triangulation
  static TriangulationResult _fanTriangulation(List<Point> points) {
    final triangles = <Triangle>[];
    double totalArea = 0.0;
    bool isValid = true;

    // نقطه مرجع (معمولاً اولین نقطه)
    final pivot = points[0];

    // ایجاد مثلث‌ها از نقطه مرجع
    for (int i = 1; i < points.length - 1; i++) {
      final triangle = Triangle(
        a: pivot,
        b: points[i],
        c: points[i + 1],
      );

      triangles.add(triangle);
      
      if (triangle.isValid) {
        totalArea += triangle.area;
      } else {
        isValid = false;
      }
    }

    return TriangulationResult(
      triangles: triangles,
      totalArea: totalArea,
      isValid: isValid,
    );
  }

  /// تبدیل واحدهای مساحت
  static Map<String, double> convertArea(double areaInSquareMeters) {
    return {
      'متر مربع': areaInSquareMeters,
      'هکتار': areaInSquareMeters / 10000,
      'جریب': areaInSquareMeters / 2000, // جریب افغانستان تقریباً ۲۰۰۰ متر مربع
      'کیلومتر مربع': areaInSquareMeters / 1000000,
    };
  }
}

/// نتیجه تقسیم‌بندی چندضلعی به مثلث‌ها
class TriangulationResult {
  final List<Triangle> triangles;
  final double totalArea;
  final bool isValid;

  const TriangulationResult({
    required this.triangles,
    required this.totalArea,
    required this.isValid,
  });

  /// تعداد مثلث‌ها
  int get triangleCount => triangles.length;

  /// لیست مساحت هر مثلث
  List<double> get individualAreas => triangles.map((t) => t.area).toList();

  @override
  String toString() {
    return 'TriangulationResult(triangles: ${triangles.length}, totalArea: ${totalArea.toStringAsFixed(2)}, isValid: $isValid)';
  }
}
