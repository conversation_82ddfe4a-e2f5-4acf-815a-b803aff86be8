import 'dart:math';

/// مدل نقطه برای نمایش مختصات
class Point {
  final double x;
  final double y;

  const Point({required this.x, required this.y});

  /// محاسبه فاصله بین دو نقطه
  double distanceTo(Point other) {
    final dx = x - other.x;
    final dy = y - other.y;
    return sqrt(dx * dx + dy * dy);
  }

  /// تبدیل به رشته برای نمایش
  @override
  String toString() {
    return 'Point(${x.toStringAsFixed(2)}, ${y.toStringAsFixed(2)})';
  }

  /// مقایسه دو نقطه
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Point && other.x == x && other.y == y;
  }

  @override
  int get hashCode => x.hashCode ^ y.hashCode;

  /// کپی کردن نقطه با تغییرات
  Point copyWith({double? x, double? y}) {
    return Point(x: x ?? this.x, y: y ?? this.y);
  }
}
