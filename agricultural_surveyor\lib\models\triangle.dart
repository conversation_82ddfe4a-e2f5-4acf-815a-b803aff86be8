import 'dart:math';
import 'point.dart';

/// مدل مثلث برای محاسبه مساحت
class Triangle {
  final Point a;
  final Point b;
  final Point c;

  const Triangle({
    required this.a,
    required this.b,
    required this.c,
  });

  /// محاسبه طول ضلع‌های مثلث
  double get sideAB => a.distanceTo(b);
  double get sideBC => b.distanceTo(c);
  double get sideCA => c.distanceTo(a);

  /// محاسبه نیم محیط مثلث
  double get semiPerimeter => (sideAB + sideBC + sideCA) / 2;

  /// محاسبه مساحت با استفاده از فرمول هرون
  /// مساحت = √[s(s-a)(s-b)(s-c)]
  /// که s = نیم محیط
  double get area {
    final s = semiPerimeter;
    final a = sideAB;
    final b = sideBC;
    final c = sideCA;

    // بررسی اینکه آیا مثلث معتبر است یا نه
    if (a + b <= c || b + c <= a || c + a <= b) {
      return 0.0; // مثلث نامعتبر
    }

    final areaSquared = s * (s - a) * (s - b) * (s - c);
    
    // بررسی اینکه مقدار زیر رادیکال منفی نباشد
    if (areaSquared < 0) {
      return 0.0;
    }

    return sqrt(areaSquared);
  }

  /// بررسی معتبر بودن مثلث
  bool get isValid {
    final a = sideAB;
    final b = sideBC;
    final c = sideCA;
    
    return a + b > c && b + c > a && c + a > b;
  }

  /// محاسبه محیط مثلث
  double get perimeter => sideAB + sideBC + sideCA;

  /// تبدیل به رشته برای نمایش
  @override
  String toString() {
    return 'Triangle(A: $a, B: $b, C: $c, Area: ${area.toStringAsFixed(2)})';
  }

  /// مقایسه دو مثلث
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Triangle && 
           other.a == a && 
           other.b == b && 
           other.c == c;
  }

  @override
  int get hashCode => a.hashCode ^ b.hashCode ^ c.hashCode;
}
